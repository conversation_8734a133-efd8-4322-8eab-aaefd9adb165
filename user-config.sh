#!/bin/bash

EVO_DIR=/run/media/qwrobins/V4T
WP_DIR=~/Pictures/wallpapers

git config --global color.ui true
git config --global user.name "Q"
git config --global user.email <EMAIL>

if [[ ! -d ~/.oh-my-zsh ]]; then
  yay -S zsh --noconfirm
  sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
  git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting
  git clone https://github.com/lukechilds/zsh-nvm ~/.oh-my-zsh/custom/plugins/zsh-nvm
  git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
  git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k
  chsh -s /bin/zsh
fi

# Restore .zshrc from backup - this contains all your custom aliases and configurations
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ZSHRC_BACKUP="$SCRIPT_DIR/.zshrc"

if [[ -f $ZSHRC_BACKUP ]]; then
  echo "Restoring complete .zshrc from local backup..."
  cp "$ZSHRC_BACKUP" ~/.zshrc
  echo ".zshrc restored successfully!"
else
  echo "WARNING: No backup .zshrc found at $ZSHRC_BACKUP"
  echo "You'll need to manually configure your shell or provide the backup file"
  # Minimal fallback configuration
  if ! grep -q "zsh-nvm zsh-syntax-highlighting zsh-autosuggestions" ~/.zshrc; then
    sed -i "/plugins=(git)/a plugins+=(zsh-nvm zsh-syntax-highlighting zsh-autosuggestions)" ~/.zshrc
  fi
fi

if [[ ! -d $WP_DIR ]]; then
  mkdir -p ~/Pictures/wallpapers
  cp $EVO_DIR/WP/5k/* ~/Pictures/wallpapers/
fi

if [[ `cat ~/.zshrc | grep -i "export EDITOR='code --wait'"` == '' ]]; then
  echo "export EDITOR='code --wait'" >> ~/.zshrc
fi
