
#!/bin/bash

EVO_DIR=/run/media/qwrobins/V4T
BACKUPS_DIR=/run/media/qwrobins/V4T/backups/home
WP_DIR=~/Pictures/wallpapers
AWS_DIR=~/.aws
SSH_DIR=~/.ssh

if [[ ! -d $AWS_DIR ]]; then
  mkdir $AWS_DIR
  cp $BACKUPS_DIR/.aws/* $AWS_DIR
fi

if [[ ! -f /usr/local/bin/aws ]]; then
  curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
  unzip awscliv2.zip
  sudo ./aws/install
  rm -rf awscliv2.zip aws/
fi
