#!/bin/bash

# Arch Linux Development Environment Setup Master Script
# This script orchestrates the complete setup of your development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to run a script with error handling
run_script() {
    local script_name="$1"
    local description="$2"
    local script_path="$SCRIPT_DIR/$script_name"
    
    if [[ ! -f "$script_path" ]]; then
        error "Script not found: $script_path"
        return 1
    fi
    
    if [[ ! -x "$script_path" ]]; then
        log "Making $script_name executable..."
        chmod +x "$script_path"
    fi
    
    log "Running: $description"
    echo "----------------------------------------"
    
    if bash "$script_path"; then
        success "$description completed successfully"
    else
        error "$description failed"
        read -p "Continue with remaining scripts? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    echo
}

# Function to check if running as root
check_not_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root!"
        error "Run as your regular user - scripts will use sudo when needed."
        exit 1
    fi
}

# Function to check if external drive is mounted
check_external_drive() {
    if [[ ! -d "/run/media/qwrobins/V4T" ]]; then
        warning "External drive not detected at /run/media/qwrobins/V4T"
        warning "Some backup restoration features may not work"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        success "External drive detected"
    fi
}

# Main setup function
main() {
    clear
    echo "=========================================="
    echo "🚀 Arch Linux Development Environment Setup"
    echo "=========================================="
    echo
    
    log "Starting complete system setup..."
    echo
    
    # Pre-flight checks
    log "Performing pre-flight checks..."
    check_not_root
    check_external_drive
    
    echo
    log "Setup will proceed in the following order:"
    echo "  1. System Configuration"
    echo "  2. Basic Packages"
    echo "  3. AUR Helper & Packages"
    echo "  4. User Configuration & Shell Setup"
    echo "  5. Development Tools (Go, Rust, NVM)"
    echo "  6. AWS Configuration"
    echo "  7. SSH Configuration"
    echo "  8. Virtualization Tools"
    echo
    
    read -p "Continue with setup? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log "Setup cancelled by user"
        exit 0
    fi
    
    echo
    log "🎯 Beginning setup process..."
    echo
    
    # Phase 1: System Configuration
    log "📋 Phase 1: System Configuration"
    run_script "system-config.sh" "System configuration (sudoers, file watchers, timezone)"
    
    # Phase 2: Basic Packages
    log "📦 Phase 2: Basic System Packages"
    run_script "packages.sh" "Basic system packages installation"
    
    # Phase 3: AUR Helper and Packages
    log "🔧 Phase 3: AUR Helper & Development Packages"
    run_script "yay.sh" "AUR helper installation and development packages"
    
    # Phase 4: User Configuration
    log "👤 Phase 4: User Configuration & Shell Setup"
    run_script "user-config.sh" "User configuration, oh-my-zsh, and .zshrc restoration"
    
    # Phase 5: Development Tools
    log "💻 Phase 5: Development Environment"
    run_script "nvm.sh" "Node.js version manager installation"
    run_script "go.sh" "Go programming language installation"
    run_script "rust.sh" "Rust programming language installation"
    
    # Phase 6: Cloud & Security Tools
    log "☁️  Phase 6: Cloud & Security Configuration"
    run_script "aws.sh" "AWS configuration restoration"
    run_script "ssh.sh" "SSH keys and configuration restoration"
    
    # Phase 7: Virtualization (Optional)
    log "🖥️  Phase 7: Virtualization Tools"
    echo "Virtualization tools (QEMU, libvirt, etc.) are optional."
    read -p "Install virtualization tools? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_script "virt.sh" "Virtualization tools installation"
    else
        log "Skipping virtualization tools"
    fi
    
    # Completion
    echo
    echo "=========================================="
    success "🎉 Setup Complete!"
    echo "=========================================="
    echo
    log "Your Arch Linux development environment is now configured!"
    echo
    echo "📋 What was installed/configured:"
    echo "  ✅ System configuration and basic packages"
    echo "  ✅ Development tools (VS Code, DataGrip, etc.)"
    echo "  ✅ Programming languages (Go, Rust, Node.js)"
    echo "  ✅ DevOps tools (kubectl, terraform, docker, etc.)"
    echo "  ✅ Cloud tools (AWS CLI, Google Cloud CLI)"
    echo "  ✅ Your complete shell configuration (.zshrc)"
    echo "  ✅ SSH keys and AWS credentials"
    echo
    warning "🔄 IMPORTANT: You should restart your terminal or run 'source ~/.zshrc' to load your shell configuration"
    echo
    log "🚀 Your development environment is ready!"
}

# Run main function
main "$@"
