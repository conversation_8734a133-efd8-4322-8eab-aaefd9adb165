#!/bin/bash

if [[ ! -d ~/development ]]; then
  mkdir -p ~/development/go/src/github.com/qwrobins
fi

if [[ ! -f /usr/local/go/bin/go ]]; then
  # Get the latest Go version from the official API
  LATEST_GO_VERSION=$(curl -s https://go.dev/VERSION?m=text | head -1)
  echo "Installing latest Go version: $LATEST_GO_VERSION"

  # Download and install the latest version
  wget https://go.dev/dl/${LATEST_GO_VERSION}.linux-amd64.tar.gz
  sudo tar -C /usr/local -xzf ${LATEST_GO_VERSION}.linux-amd64.tar.gz

  # Clean up downloaded file
  rm ${LATEST_GO_VERSION}.linux-amd64.tar.gz

  # Add Go environment to bashrc if not already present
  if ! grep -q "export PATH.*go/bin" ~/.bashrc; then
    echo 'export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin:/home/<USER>/.local/bin' >> ~/.bashrc
  fi
  if ! grep -q "export GOPATH" ~/.bashrc; then
    echo 'export GOPATH=/home/<USER>/development/go' >> ~/.bashrc
  fi

  # Add Go environment to zshrc if not already present
  if ! grep -q "export GO111MODULE" ~/.zshrc; then
    echo 'export GO111MODULE=on' >> ~/.zshrc
  fi
  if ! grep -q "export PATH.*go/bin" ~/.zshrc; then
    echo 'export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin' >> ~/.zshrc
  fi
  if ! grep -q "export GOPATH" ~/.zshrc; then
    echo 'export GOPATH=/home/<USER>/development/go' >> ~/.zshrc
  fi
fi

export GOPATH=/home/<USER>/development/go
export GO111MODULE=on
export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin
