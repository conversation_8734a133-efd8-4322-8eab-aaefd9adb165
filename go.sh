#!/bin/bash

EVO_DIR=/media/qwrobins/V4T
BACKUPS_DIR=//media/qwrobins/V4T/backups/g950/qwrobins
WP_DIR=~/Pictures/wallpapers
AWS_DIR=~/.aws
SSH_DIR=~/.ssh

if [[ ! -d ~/development ]]; then
  mkdir -p ~/development/go/src/github.com/qwrobins
fi

if [[ ! -f /usr/local/go/bin/go ]]; then
  # Get the latest Go version from the official API
  LATEST_GO_VERSION=$(curl -s https://go.dev/VERSION?m=text | head -1)
  echo "Installing latest Go version: $LATEST_GO_VERSION"

  # Download and install the latest version
  wget https://go.dev/dl/${LATEST_GO_VERSION}.linux-amd64.tar.gz
  sudo tar -C /usr/local -xzf ${LATEST_GO_VERSION}.linux-amd64.tar.gz

  # Clean up downloaded file
  rm ${LATEST_GO_VERSION}.linux-amd64.tar.gz

  echo 'export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin:/home/<USER>/.local/bin' >> ~/.bashrc
  echo 'export GOPATH=/home/<USER>/development/go' >> ~/.bashrc
  echo 'export GO111MODULE=on' >> $HOME/.zshrc
  echo 'export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin' >> ~/.zshrc
  echo 'export GOPATH=/home/<USER>/development/go' >> ~/.zshrc
fi

export GOPATH=/home/<USER>/development/go
export GO111MODULE=on
export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin
