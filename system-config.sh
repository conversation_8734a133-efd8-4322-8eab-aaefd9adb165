#!/bin/bash

EVO_DIR=/run/media/qwrobins/V4T
BACKUPS_DIR=/run/media/qwrobins/V4T/backups/g950/qwrobins
WP_DIR=~/Pictures/wallpapers
AWS_DIR=~/.aws
SSH_DIR=~/.ssh
QWR_SUDOERS=`sudo grep -c qwrobins /etc/sudoers`
F_WATCHERS=`sudo grep -c fs.inotify.max_user_watches=524288 /etc/sysctl.conf`


if [[ $QWR_SUDOERS < '1' ]]; then
  echo 'Updating /etc/sudoers'
  sudo chmod 0700 /etc/sudoers
  sudo sed -i -e "\$aqwrobins ALL=(ALL) NOPASSWD: ALL" /etc/sudoers
fi

if [[ $F_WATCHERS < '1' ]]; then
  sudo sed -i -e "\$afs.inotify.max_user_watches=524288" /etc/sysctl.conf
fi

sudo timedatectl set-local-rtc 1 --adjust-system-clock
