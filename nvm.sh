#!/bin/bash

EVO_DIR=/media/qwrobins/V4T
BACKUPS_DIR=//media/qwrobins/V4T/backups/g950/qwrobins
WP_DIR=~/Pictures/wallpapers
AWS_DIR=~/.aws
SSH_DIR=~/.ssh

if [[ ! -d  ~/.nvm ]]; then
  # Get the latest NVM version from GitHub API
  LATEST_NVM_VERSION=$(curl -s https://api.github.com/repos/nvm-sh/nvm/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')
  echo "Installing latest NVM version: $LATEST_NVM_VERSION"

  # Install the latest version of NVM
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/${LATEST_NVM_VERSION}/install.sh | bash
  export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
  nvm install --lts
fi
