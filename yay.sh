#!/bin/bash

# Exit on any error
set -e

# Function to check if yay is installed
check_yay() {
    if ! command -v yay &> /dev/null; then
        echo "Installing yay AUR helper..."

        # Install prerequisites
        sudo pacman -S --needed git base-devel --noconfirm

        # Clone and build yay
        cd /tmp
        git clone https://aur.archlinux.org/yay.git
        cd yay
        makepkg -si --noconfirm

        # Cleanup
        cd /
        rm -rf /tmp/yay

        echo "yay installed successfully!"
    else
        echo "yay is already installed"
    fi
}

# Function to install packages with error handling
install_packages() {
    local category="$1"
    shift
    local packages=("$@")

    echo "Installing $category packages..."
    for package in "${packages[@]}"; do
        echo "Installing: $package"
        if ! yay -S "$package" --noconfirm; then
            echo "Warning: Failed to install $package, continuing..."
        fi
    done
    echo "$category packages installation completed."
}

# Install yay if not present
check_yay

# Development Tools
dev_tools=(
    "anaconda"
    "visual-studio-code-bin"
    "cursor-bin"
    "datagrip"
    "claude-code"
    "bun-bin"
    "nodejs-nativefier"
)

# Browsers
browsers=(
    "google-chrome"
    "brave-bin"
    "zen-browser-bin"
)

# Communication & Productivity
communication=(
    "slack-desktop"
    "telegram-desktop"
    "zoom"
)

# Security & Cloud Tools
security_utils=(
    "1password"
    "aws-vault"
    "aws-cli-v2"
    "google-cloud-cli"
    "ngrok"
)

# DevOps & Cloud Tools
devops_tools=(
    "kind"
    "helm"
    "kubectl"
    "kubectx"
    "stern"
    "k9s"
    "terraform"
)

# System Utilities
system_utils=(
    "fastfetch"
    "eza"
    "yay"
)

# Virtualization & System Tools
virtualization=(
    "vmware-workstation"
    "vmware-keymaps"
    "uefitool"
)

# Finance & Crypto
finance=(
    "exodus"
)

# Print & Hardware
hardware=(
    "ufrii-print"
)

# Gaming
gaming=(
    "steam"
)

# Install packages by category
install_packages "Development Tools" "${dev_tools[@]}"
install_packages "Browsers" "${browsers[@]}"
install_packages "Communication & Productivity" "${communication[@]}"
install_packages "Security & Cloud Tools" "${security_utils[@]}"
install_packages "DevOps & Cloud Tools" "${devops_tools[@]}"
install_packages "System Utilities" "${system_utils[@]}"
install_packages "Virtualization & System Tools" "${virtualization[@]}"
install_packages "Finance & Crypto" "${finance[@]}"
install_packages "Print & Hardware" "${hardware[@]}"
install_packages "Gaming" "${gaming[@]}"

echo "All package installations completed!"
